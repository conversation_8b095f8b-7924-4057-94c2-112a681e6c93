'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Expand, Eye, Monitor, Settings, Smartphone } from 'lucide-react'
import React, { useCallback, useMemo, useState } from 'react'
import { TEMPLATES } from '../../constants/templates'
import { useCreateBookingPageV2Store } from '../../stores/create-booking-page-v2.store'
import { ClassicSportConfig, ModernSportConfig } from '../templates/configs'
import LazyTemplatePreview from '../templates/LazyTemplatePreview'

export const BookingConfigStep: React.FC = () => {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop')
  const [fullscreenPreviewMode, setFullscreenPreviewMode] = useState<'desktop' | 'mobile'>('desktop')

  // Use direct store access
  const pageInfo = useCreateBookingPageV2Store(state => state.pageInfo)
  const selectedTemplateId = useCreateBookingPageV2Store(state => state.selectedTemplateId)
  const bookingConfig = useCreateBookingPageV2Store(state => state.bookingConfig)

  // Memoize selected template
  const selectedTemplate = useMemo(() =>
    TEMPLATES.find(t => t.id === selectedTemplateId), [selectedTemplateId])

  // Memoize template preview props
  const templatePreviewProps = useMemo(() => ({
    config: bookingConfig,
    pageInfo,
    previewMode,
  }), [bookingConfig, pageInfo, previewMode])

  // Memoized template preview component with lazy loading
  const TemplatePreview = useMemo(() => (
    <LazyTemplatePreview
      templateId={selectedTemplateId}
      {...templatePreviewProps}
    />
  ), [selectedTemplateId, templatePreviewProps])

  // Memoized template configuration component
  const TemplateConfig = useMemo(() => {
    switch (selectedTemplateId) {
      case 'sport-modern':
        return <ModernSportConfig />
      case 'sport-classic':
        return <ClassicSportConfig />
      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Vui lòng chọn template để cấu hình</p>
          </div>
        )
    }
  }, [selectedTemplateId])

  // Memoized preview mode handlers
  const handleDesktopMode = useCallback(() => setPreviewMode('desktop'), [])
  const handleMobileMode = useCallback(() => setPreviewMode('mobile'), [])

  // Fullscreen preview mode handlers
  const handleFullscreenDesktopMode = useCallback(() => setFullscreenPreviewMode('desktop'), [])
  const handleFullscreenMobileMode = useCallback(() => setFullscreenPreviewMode('mobile'), [])

  return (
    <div className="animate-in fade-in slide-in-from-right-4 duration-300">
      <div className="mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 flex items-center justify-center gap-3 mb-4">
            <Settings className="w-7 h-7 lg:w-8 lg:h-8 text-orange-500" />
            Cấu hình trang đặt chỗ
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-10 gap-8">
          {/* Configuration Panel */}
          <div className="space-y-6 lg:col-span-3">
            <Card className="border-orange-200">
              <CardHeader className="flex items-center h-11 pb-0 bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200 sticky top-0 z-10">
                <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-3">
                  <Settings className="w-5 h-5 text-orange-500" />
                  Cấu hình template
                  {' '}
                  {selectedTemplate && (
                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-100 text-orange-700 rounded-full text-sm font-medium">
                      <Eye className="w-4 h-4" />
                      Template:
                      {' '}
                      {selectedTemplate.name}
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 max-h-[calc(100vh-200px)] overflow-y-auto">
                {TemplateConfig}
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel - Sticky */}
          <div className="space-y-6 lg:col-span-7 lg:sticky lg:top-8 lg:h-fit">
            <Card className="border-orange-200">
              <CardHeader className="h-11 bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-3">
                    <Eye className="w-5 h-5 text-orange-500" />
                    Xem trước
                  </CardTitle>

                  {/* Preview Controls */}
                  <div className="flex items-center gap-2">
                    {/* Preview Mode Toggle */}
                    <div className="flex items-center gap-1 bg-white rounded-lg p-1 border border-gray-200">
                      <Button
                        type="button"
                        variant={previewMode === 'desktop' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={handleDesktopMode}
                        className="h-8 w-8 p-0"
                      >
                        <Monitor className="w-4 h-4" />
                      </Button>
                      <Button
                        type="button"
                        variant={previewMode === 'mobile' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={handleMobileMode}
                        className="h-8 w-8 p-0"
                      >
                        <Smartphone className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Fullscreen Button */}
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          title="Xem toàn màn hình"
                        >
                          <Expand className="w-4 h-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="!max-w-none max-h-none !w-screen h-screen m-0 p-0 rounded-none border-none">
                        <DialogHeader className="p-4 border-b bg-white">
                          <div className="flex items-center justify-between">
                            <DialogTitle className="flex items-center gap-2">
                              <Eye className="w-5 h-5 text-orange-500" />
                              Xem trước toàn màn hình
                            </DialogTitle>

                            {/* Fullscreen Preview Mode Toggle */}
                            <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1 mr-4">
                              <Button
                                type="button"
                                variant={fullscreenPreviewMode === 'desktop' ? 'default' : 'ghost'}
                                size="sm"
                                onClick={handleFullscreenDesktopMode}
                                className="h-8 w-8 p-0"
                              >
                                <Monitor className="w-4 h-4" />
                              </Button>
                              <Button
                                type="button"
                                variant={fullscreenPreviewMode === 'mobile' ? 'default' : 'ghost'}
                                size="sm"
                                onClick={handleFullscreenMobileMode}
                                className="h-8 w-8 p-0"
                              >
                                <Smartphone className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </DialogHeader>
                        <div className="flex-1 overflow-auto bg-gray-50 p-4">
                          <div className={`mx-auto transition-all duration-300 ${
                            fullscreenPreviewMode === 'mobile' ? 'max-w-sm' : 'max-w-none'
                          }`}
                          >
                            <LazyTemplatePreview
                              templateId={selectedTemplateId}
                              config={bookingConfig}
                              pageInfo={pageInfo}
                              previewMode={fullscreenPreviewMode}
                            />
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="">
                <div className={`bg-gray-50 rounded-xl p-4 max-h-[calc(100vh-300px)] overflow-y-auto will-change-transform ${
                  previewMode === 'mobile' ? 'max-w-sm mx-auto transition-all duration-300' : 'w-full'
                }`}
                >
                  <div className="min-h-[400px]">
                    {TemplatePreview}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingConfigStep
