import type { BookingConfig, BookingField, BookingFormConfig, CreateBookingPageV2State, PageInfo } from '../types'
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { validateStep } from '../utils/validation'

const initialPageInfo: PageInfo = {
  name: '',
  description: '',
  slug: '',
}

const initialBookingConfig: BookingConfig = {
  // Banner settings
  bannerTitle: '',
  bannerSubtitle: '',
  bannerImage: '',

  // Operating hours
  openTime: '06:00',
  closeTime: '22:00',

  // Fields configuration
  fields: [],

  // Description settings
  description: '',
  location: '',

  // Contact information
  contactInfo: {
    phone: '',
    email: '',
    address: '',
    socialLinks: {
      facebook: '',
      instagram: '',
      website: '',
    },
  },

  // Pricing configuration
  pricing: {
    basePrice: 300000,
    currency: 'VNĐ',
    priceUnit: 'hour',
    showPricing: true,
  },

  // Booking form configuration
  bookingForm: {
    enabled: true,
    title: 'Thông tin đặt chỗ',
    subtitle: 'Vui lòng điền thông tin để hoàn tất đặt chỗ',
    fields: [
      {
        id: 'name',
        name: 'name',
        label: 'Họ và tên',
        type: 'text',
        required: true,
        placeholder: 'Nhập họ và tên của bạn',
      },
      {
        id: 'email',
        name: 'email',
        label: 'Email',
        type: 'email',
        required: true,
        placeholder: 'Nhập địa chỉ email',
      },
      {
        id: 'phone',
        name: 'phone',
        label: 'Số điện thoại',
        type: 'phone',
        required: true,
        placeholder: 'Nhập số điện thoại',
      },
    ],
    showBookingSummary: true,
    submitButtonText: 'Xác nhận đặt chỗ',
  },

  // Display settings
  showCapacity: true,
  showFieldTypes: true,
  showDirections: true,
}

export const useCreateBookingPageV2Store = create<CreateBookingPageV2State>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        currentStep: 1,
        pageInfo: initialPageInfo,
        selectedTemplateId: '',
        bookingConfig: initialBookingConfig,
        isLoading: false,
        errors: {},

        // Step navigation
        setCurrentStep: (step: number) => {
          set({ currentStep: step }, false, 'setCurrentStep')
        },

        nextStep: () => {
          const { currentStep } = get()
          if (currentStep < 3) {
            set({ currentStep: currentStep + 1, errors: {} }, false, 'nextStep')
          }
        },

        prevStep: () => {
          const { currentStep } = get()
          if (currentStep > 1) {
            set({ currentStep: currentStep - 1, errors: {} }, false, 'prevStep')
          }
        },

        // Page info actions
        updatePageInfo: (info: Partial<PageInfo>) => {
          set(
            state => ({
              pageInfo: { ...state.pageInfo, ...info },
              errors: { ...state.errors, ...Object.keys(info).reduce((acc, key) => ({ ...acc, [key]: '' }), {}) },
            }),
            false,
            'updatePageInfo',
          )
        },

        // Template actions
        setSelectedTemplate: (templateId: string) => {
          set(
            { selectedTemplateId: templateId, errors: { ...get().errors, selectedTemplateId: '' } },
            false,
            'setSelectedTemplate',
          )
        },

        // Booking config actions
        updateBookingConfig: (config: Partial<BookingConfig>) => {
          set(
            (state) => {
              // Only update if values actually changed
              const hasChanges = Object.keys(config).some(key =>
                state.bookingConfig[key as keyof BookingConfig] !== config[key as keyof BookingConfig],
              )

              if (!hasChanges) {
                return state
              }

              return {
                bookingConfig: { ...state.bookingConfig, ...config },
                errors: { ...state.errors, ...Object.keys(config).reduce((acc, key) => ({ ...acc, [key]: '' }), {}) },
              }
            },
            false,
            'updateBookingConfig',
          )
        },

        addField: () => {
          const { bookingConfig } = get()
          const newField: BookingField = {
            id: `field-${Date.now()}`,
            name: `Sân ${bookingConfig.fields.length + 1}`,
            type: 'football',
            capacity: 1,
          }

          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: [...state.bookingConfig.fields, newField],
              },
              errors: { ...state.errors, fields: '' },
            }),
            false,
            'addField',
          )
        },

        removeField: (fieldId: string) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: state.bookingConfig.fields.filter(field => field.id !== fieldId),
              },
            }),
            false,
            'removeField',
          )
        },

        updateField: (fieldId: string, updates: Partial<BookingField>) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: state.bookingConfig.fields.map(field =>
                  field.id === fieldId ? { ...field, ...updates } : field,
                ),
              },
            }),
            false,
            'updateField',
          )
        },

        // Booking Form Management
        updateBookingForm: (updates: Partial<BookingFormConfig>) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                bookingForm: {
                  ...state.bookingConfig.bookingForm,
                  ...updates,
                },
              },
            }),
            false,
            'updateBookingForm',
          )
        },

        addBookingFormField: () => {
          const newField = {
            id: `field-${Date.now()}`,
            name: `field-${Date.now()}`,
            label: 'Trường mới',
            type: 'text' as const,
            required: false,
            placeholder: '',
          }

          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                bookingForm: {
                  ...state.bookingConfig.bookingForm,
                  fields: [...state.bookingConfig.bookingForm.fields, newField],
                },
              },
            }),
            false,
            'addBookingFormField',
          )
        },

        removeBookingFormField: (fieldId: string) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                bookingForm: {
                  ...state.bookingConfig.bookingForm,
                  fields: state.bookingConfig.bookingForm.fields.filter(field => field.id !== fieldId),
                },
              },
            }),
            false,
            'removeBookingFormField',
          )
        },

        updateBookingFormField: (fieldId: string, updates: Partial<any>) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                bookingForm: {
                  ...state.bookingConfig.bookingForm,
                  fields: state.bookingConfig.bookingForm.fields.map(field =>
                    field.id === fieldId ? { ...field, ...updates } : field,
                  ),
                },
              },
            }),
            false,
            'updateBookingFormField',
          )
        },

        // Validation
        validateCurrentStep: () => {
          const { currentStep, pageInfo, selectedTemplateId, bookingConfig } = get()

          let data: any // Used in switch statement below
          switch (currentStep) {
            case 1:
              data = pageInfo
              break
            case 2:
              data = { selectedTemplateId }
              break
            case 3:
              data = bookingConfig
              break
            default:
              return false
          }

          // if (!isValid) {
          //   set({ errors }, false, 'validateCurrentStep')
          // }

          return true
        },

        getStepErrors: (step: number) => {
          const { pageInfo, selectedTemplateId, bookingConfig } = get()

          let data: any
          switch (step) {
            case 1:
              data = pageInfo
              break
            case 2: // Fixed: was case 3 before
              data = { selectedTemplateId }
              break
            case 3: // Fixed: was case 4 before
              data = bookingConfig
              break
            default:
              return []
          }

          const { errors } = validateStep(step, data)
          return Object.values(errors).filter(Boolean) as string[]
        },

        // Reset
        reset: () => {
          set(
            {
              currentStep: 1,
              pageInfo: initialPageInfo,
              selectedTemplateId: '',
              bookingConfig: initialBookingConfig,
              isLoading: false,
              errors: {},
            },
            false,
            'reset',
          )
        },

        // Clear localStorage
        clearStorage: () => {
          localStorage.removeItem('create-booking-page-v2-store')
          get().reset()
        },
      }),
      {
        name: 'create-booking-page-v2-store',
        // Chỉ persist những state cần thiết, không persist errors và isLoading
        partialize: state => ({
          currentStep: state.currentStep,
          pageInfo: state.pageInfo,
          selectedTemplateId: state.selectedTemplateId,
          bookingConfig: state.bookingConfig,
        }),
        // Version để handle migration khi structure thay đổi
        version: 2,
        migrate: (persistedState: any, version: number) => {
          // Handle migration logic nếu cần
          if (version === 0 || version === 1) {
            // Migration from version 0/1 to 2 - add new config fields
            const migratedState = {
              ...persistedState,
              bookingConfig: {
                ...persistedState.bookingConfig,
                // Add new fields with defaults if they don't exist
                description: persistedState.bookingConfig?.description || '',
                location: persistedState.bookingConfig?.location || '',
                contactInfo: persistedState.bookingConfig?.contactInfo || {
                  phone: '',
                  email: '',
                  address: '',
                  socialLinks: {
                    facebook: '',
                    instagram: '',
                    website: '',
                  },
                },
                pricing: persistedState.bookingConfig?.pricing || {
                  basePrice: 300000,
                  currency: 'VNĐ',
                  priceUnit: 'hour',
                  showPricing: true,
                },
                showCapacity: persistedState.bookingConfig?.showCapacity ?? true,
                showFieldTypes: persistedState.bookingConfig?.showFieldTypes ?? true,
                showDirections: persistedState.bookingConfig?.showDirections ?? true,
              },
            }
            return migratedState
          }
          return persistedState as CreateBookingPageV2State
        },
      },
    ),
    {
      name: 'create-booking-page-v2-store',
    },
  ),
)
