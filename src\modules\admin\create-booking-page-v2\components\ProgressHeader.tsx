'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Check, ChevronLeft, ChevronRight, FileText, Pa<PERSON>, Settings } from 'lucide-react'
import React from 'react'
import { pageInfoSchema } from '../schemas/form-schemas'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

const STEPS = [
  { id: 1, title: 'Thông tin', icon: FileText },
  { id: 2, title: 'Chọn mẫu', icon: Palette },
  { id: 3, title: 'C<PERSON>u hình', icon: Settings },
]

const TOTAL_STEPS = 3

export const ProgressHeader: React.FC = () => {
  const {
    currentStep,
    nextStep,
    prevStep,
    validateCurrentStep,
    pageInfo,
    selectedTemplateId,
    bookingConfig,
  } = useCreateBookingPageV2Store()

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return pageInfoSchema.safeParse(pageInfo)?.success
      case 2:
        return selectedTemplateId !== ''
      case 3:
        return (
          bookingConfig.bannerTitle.trim() !== ''
          && bookingConfig.openTime !== ''
          && bookingConfig.closeTime !== ''
          && bookingConfig.fields.length > 0
        )
      default:
        return false
    }
  }

  const handleNext = () => {
    if (validateCurrentStep()) {
      nextStep()
    }
  }

  const handleFinish = () => {
    if (validateCurrentStep()) {
      // TODO: Implement create booking page logic
    }
  }

  return (
    <div className="bg-white/90 backdrop-blur-md border-b border-orange-200/50 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-2 lg:py-2">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Navigation & Title */}
          <div className="flex items-center gap-4">
            {/* Back Button */}
            <Button
              onClick={prevStep}
              disabled={currentStep === 1}
              variant="outline"
              size="sm"
              className="border-orange-300 text-orange-600 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>

            {/* Title */}
            <div className="text-center lg:text-left">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                Tạo trang đặt chỗ mới
              </h1>
            </div>
          </div>

          {/* Progress Steps & Next Button - Desktop */}
          <div className="hidden lg:flex items-center space-x-4">
            {/* Progress Steps */}
            <div className="flex items-center space-x-4">
              {STEPS.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id

                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`
                      flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                      ${isActive
                    ? 'bg-orange-500 border-orange-500 text-white shadow-lg'
                    : isCompleted
                      ? 'bg-green-500 border-green-500 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                  }
                    `}
                    >
                      {isCompleted
                        ? (
                            <Check className="w-4 h-4" />
                          )
                        : (
                            <Icon className="w-4 h-4" />
                          )}
                    </div>
                    <div className="ml-3">
                      <div className={`text-sm font-medium ${
                        isActive ? 'text-orange-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                      }`}
                      >
                        Bước
                        {' '}
                        {step.id}
                      </div>
                      <div className={`text-xs ${
                        isActive ? 'text-orange-500' : isCompleted ? 'text-green-500' : 'text-gray-400'
                      }`}
                      >
                        {step.title}
                      </div>
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                    )}
                  </div>
                )
              })}
            </div>

            {/* Next/Finish Button */}
            {currentStep < TOTAL_STEPS
              ? (
                  <Button
                    onClick={handleNext}
                    disabled={!canProceed()}
                    size="sm"
                    className="bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                  >
                    Tiếp theo
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </Button>
                )
              : (
                  <Button
                    onClick={handleFinish}
                    disabled={!canProceed()}
                    size="sm"
                    className="bg-green-500 hover:bg-green-600 text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                  >
                    <Check className="w-4 h-4 mr-1" />
                    Tạo trang
                  </Button>
                )}
          </div>

          {/* Progress Steps & Navigation - Mobile */}
          <div className="flex lg:hidden justify-between items-center w-full">
            {/* Back Button - Mobile */}
            <Button
              onClick={prevStep}
              disabled={currentStep === 1}
              variant="outline"
              size="sm"
              className="border-orange-300 text-orange-600 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>

            {/* Progress Steps */}
            <div className="flex items-center space-x-2">
              {STEPS.map((step, index) => {
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id

                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-300
                      ${isActive
                    ? 'bg-orange-500 text-white shadow-lg'
                    : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }
                    `}
                    >
                      {isCompleted ? <Check className="w-4 h-4" /> : step.id}
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className={`w-4 h-0.5 mx-1 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                    )}
                  </div>
                )
              })}
            </div>

            {/* Next/Finish Button - Mobile */}
            {currentStep < TOTAL_STEPS
              ? (
                  <Button
                    onClick={handleNext}
                    disabled={!canProceed()}
                    size="sm"
                    className="bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                )
              : (
                  <Button
                    onClick={handleFinish}
                    disabled={!canProceed()}
                    size="sm"
                    className="bg-green-500 hover:bg-green-600 text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                  >
                    <Check className="w-4 h-4" />
                  </Button>
                )}
          </div>
        </div>
      </div>
    </div>
  )
}
