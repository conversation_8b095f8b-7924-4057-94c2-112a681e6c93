'use client'

import { Check, FileText, Palette, Settings } from 'lucide-react'
import React from 'react'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

const STEPS = [
  { id: 1, title: 'Thông tin', icon: FileText },
  { id: 2, title: '<PERSON>ọn mẫu', icon: Palette },
  { id: 3, title: '<PERSON><PERSON><PERSON> hình', icon: Settings },
]

export const ProgressHeader: React.FC = () => {
  const { currentStep } = useCreateBookingPageV2Store()

  return (
    <div className="bg-white/90 backdrop-blur-md border-b border-orange-200/50 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-2 lg:py-2">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Title */}
          <div className="text-center lg:text-left">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
              Tạo trang đặt chỗ mới
            </h1>
          </div>

          {/* Progress Steps - Desktop */}
          <div className="hidden lg:flex items-center space-x-4">
            {STEPS.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id

              return (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                    ${isActive
                  ? 'bg-orange-500 border-orange-500 text-white shadow-lg'
                  : isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                }
                  `}
                  >
                    {isCompleted
                      ? (
                          <Check className="w-4 h-4" />
                        )
                      : (
                          <Icon className="w-4 h-4" />
                        )}
                  </div>
                  <div className="ml-3">
                    <div className={`text-sm font-medium ${
                      isActive ? 'text-orange-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                    }`}
                    >
                      Bước
                      {' '}
                      {step.id}
                    </div>
                    <div className={`text-xs ${
                      isActive ? 'text-orange-500' : isCompleted ? 'text-green-500' : 'text-gray-400'
                    }`}
                    >
                      {step.title}
                    </div>
                  </div>
                  {index < STEPS.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                  )}
                </div>
              )
            })}
          </div>

          {/* Progress Steps - Mobile */}
          <div className="flex lg:hidden justify-center">
            <div className="flex items-center space-x-2">
              {STEPS.map((step, index) => {
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id

                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-300
                      ${isActive
                    ? 'bg-orange-500 text-white shadow-lg'
                    : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }
                    `}
                    >
                      {isCompleted ? <Check className="w-4 h-4" /> : step.id}
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className={`w-4 h-0.5 mx-1 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
