'use client'

import React from 'react'
import { BookingConfigStep } from '../components'
import { NavigationFooter } from '../components/NavigationFooter'
import { ProgressHeader } from '../components/ProgressHeader'
import { PageInfoStep } from '../components/steps/PageInfoStep'
import { TemplateSelectionStep } from '../components/steps/TemplateSelectionStep'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

export const CreateBookingPageV2Screen: React.FC = () => {
  const { currentStep } = useCreateBookingPageV2Store()

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <PageInfoStep />
      case 2:
        return <TemplateSelectionStep />
      case 3:
        return <BookingConfigStep />
      default:
        return <PageInfoStep />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-orange-100/30 to-orange-200/50 flex flex-col">
      {/* Progress Header */}
      <ProgressHeader />

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-12">
        <div className="transition-all duration-300">
          {renderCurrentStep()}
        </div>

        {/* Navigation Footer */}
      </div>
      <div className="sticky bottom-0 p-0 md:px-10 md:py-6">
        <NavigationFooter />
      </div>
    </div>
  )
}

export default CreateBookingPageV2Screen
